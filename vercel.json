{"version": 2, "buildCommand": "npm run vercel-build", "outputDirectory": "dist", "installCommand": "npm ci", "framework": null, "headers": [{"source": "/fonts/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Type", "value": "font/ttf"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_nuxt/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/vercel-critical-styles.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}, {"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/(.*)", "headers": [{"key": "X-Vercel-Deployment", "value": "true"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}