/**
 * Vercel关键样式立即注入脚本 - 第四次全面修复版本
 * 确保关键样式在页面加载时立即应用，避免布局闪烁
 */

(function() {
  'use strict';

  // 检测是否需要应用修复 - 在所有环境中都应用
  const needsFix = window.location.hostname.includes('vercel.app') ||
                   window.location.hostname.includes('.vercel.app') ||
                   window.location.hostname.includes('yugiohcardmaker.org') ||
                   true; // 在所有环境中都应用，确保一致性

  if (!needsFix) return;

  console.log('Applying critical Vercel styles - Fourth comprehensive fix...');

  // 创建关键样式
  const criticalCSS = `
    /* Vercel 关键布局样式 - 立即应用 */
    .editor-panel {
      display: flex !important;
      flex-direction: column !important;
      min-height: 85vh !important;
      max-height: 85vh !important;
      background: var(--bg-secondary, #1a2332) !important;
      border-radius: 16px !important;
      overflow-y: auto !important;
    }

    .editor-container {
      display: flex !important;
      flex-direction: column !important;
      flex: 1 !important;
      padding: 1rem !important;
      height: 100% !important;
    }

    .preview-panel {
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
      min-height: 85vh !important;
      max-height: 85vh !important;
      position: sticky !important;
      top: 80px !important;
      overflow: hidden !important;
      background: var(--bg-primary, #0d1421) !important;
      padding: 1.5rem !important;
    }

    .preview-container {
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      height: 100% !important;
      width: 100% !important;
      max-width: 380px !important;
      text-align: center !important;
    }

    .card-preview-wrapper {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      min-height: 400px !important;
      background: linear-gradient(145deg, var(--bg-card, #1f2937), var(--bg-tertiary, #2a3441)) !important;
      border-radius: 24px !important;
      padding: 3rem 2rem !important;
      position: relative !important;
      overflow: hidden !important;
    }

    .card-editor-section {
      background: var(--bg-primary, #0d1421) !important;
      padding: 2rem 0 !important;
      margin-top: 80px !important;
      min-height: calc(100vh - 80px) !important;
    }

    /* Canvas 修复 */
    canvas#yugiohcard,
    canvas.card-canvas,
    #yugiohcard.card-canvas,
    .card-canvas {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
      border: none !important;
      border-width: 0 !important;
      border-style: none !important;
      border-color: transparent !important;
      outline: none !important;
      max-width: 100% !important;
      height: auto !important;
      aspect-ratio: 1000 / 1450 !important;
    }

    /* 响应式修复 */
    @media (max-width: 1200px) {
      .preview-panel,
      .editor-panel {
        min-height: 75vh !important;
        max-height: 75vh !important;
      }
    }

    @media (max-width: 992px) {
      .card-editor-section {
        padding: 1rem 0 !important;
        margin-top: 70px !important;
      }

      .card-editor-section .row {
        flex-direction: column !important;
        gap: 1rem !important;
      }

      .editor-panel {
        position: static !important;
        max-height: none !important;
        min-height: auto !important;
        order: 2 !important;
        margin-bottom: 1rem !important;
      }

      .preview-panel {
        position: static !important;
        max-height: none !important;
        min-height: auto !important;
        order: 1 !important;
        margin-bottom: 1rem !important;
        top: auto !important;
      }

      .preview-container {
        max-width: 100% !important;
        height: auto !important;
      }
    }

    @media (max-width: 768px) {
      .card-editor-section {
        margin-top: 60px !important;
      }

      .card-preview-wrapper {
        padding: 2rem 1.5rem !important;
        min-height: 350px !important;
      }
    }

    @media (max-width: 480px) {
      .card-editor-section .container-fluid {
        padding: 0 0.5rem !important;
        overflow-x: hidden !important;
      }

      .card-editor-section .row {
        margin: 0 !important;
      }

      .card-editor-section .col-lg-5,
      .card-editor-section .col-lg-7,
      .card-editor-section .col-xl-6 {
        padding: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        flex: none !important;
      }

      .editor-container {
        padding: 0.75rem !important;
      }

      .preview-panel {
        padding: 0.75rem !important;
      }

      .card-preview-wrapper {
        padding: 1.5rem 1rem !important;
      }

      .px-2 {
        padding-left: 0.25rem !important;
        padding-right: 0.25rem !important;
        margin-bottom: 0.75rem !important;
      }

      .row.my-3 {
        margin: 0.75rem 0 !important;
      }
    }
  `;

  // 创建并注入样式标签
  function injectCriticalStyles() {
    const styleElement = document.createElement('style');
    styleElement.id = 'vercel-critical-styles';
    styleElement.type = 'text/css';
    styleElement.innerHTML = criticalCSS;
    
    // 插入到 head 的最前面，确保最高优先级
    const head = document.head || document.getElementsByTagName('head')[0];
    head.insertBefore(styleElement, head.firstChild);
    
    // 添加 Vercel 环境标识
    document.documentElement.classList.add('vercel-deployment', 'vercel-critical-loaded');
    document.body.classList.add('vercel-deployment', 'layout-fixed');

    console.log('Vercel critical styles injected successfully');
  }

  // 立即执行或等待 DOM 准备
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectCriticalStyles);
  } else {
    injectCriticalStyles();
  }

  // 额外的保险措施：在页面完全加载后再次检查
  window.addEventListener('load', function() {
    if (!document.getElementById('vercel-critical-styles')) {
      injectCriticalStyles();
    }
  });

  // 监听DOM变化，确保样式持续应用
  if (window.MutationObserver) {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          // 重新应用Canvas修复
          const canvases = document.querySelectorAll('canvas#yugiohcard, canvas.card-canvas, #yugiohcard.card-canvas, .card-canvas');
          canvases.forEach(canvas => {
            if (canvas && !canvas.hasAttribute('data-fixed')) {
              canvas.style.setProperty('background', 'transparent', 'important');
              canvas.style.setProperty('border', 'none', 'important');
              canvas.setAttribute('data-fixed', 'true');
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

})();
