/* Vercel部署专用修复样式 */

/* 1. 强制字体加载和显示 */
@font-face {
  font-family: 'YuGiOh-Matrix';
  src: url('/fonts/MatrixBoldSmallCaps.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-JP';
  src: url('/fonts/jp.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-JP2';
  src: url('/fonts/jp2.otf') format('opentype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-EN';
  src: url('/fonts/en.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-EN2';
  src: url('/fonts/en2.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-EN3';
  src: url('/fonts/en3.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-CN';
  src: url('/fonts/cn.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-ZH';
  src: url('/fonts/zh.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YuGiOh-Link';
  src: url('/fonts/link.ttf') format('truetype');
  font-display: swap;
  font-weight: normal;
  font-style: normal;
}

/* 2. Vercel环境下的Canvas修复 - 最高优先级 */
html[data-nuxt-ssr] canvas#yugiohcard,
html[data-nuxt-ssr] canvas.card-canvas,
html[data-nuxt-ssr] #yugiohcard.card-canvas,
html[data-nuxt-ssr] .card-canvas,
[data-server-rendered="true"] canvas#yugiohcard,
[data-server-rendered="true"] canvas.card-canvas,
[data-server-rendered="true"] #yugiohcard.card-canvas,
[data-server-rendered="true"] .card-canvas,
.nuxt-loading canvas#yugiohcard,
.nuxt-loading canvas.card-canvas,
.nuxt-loading #yugiohcard.card-canvas,
.nuxt-loading .card-canvas,
canvas#yugiohcard,
canvas.card-canvas,
#yugiohcard.card-canvas,
.card-canvas {
  /* 强制透明背景 */
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  
  /* 强制移除边框 */
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
  
  /* 确保正确的尺寸 */
  max-width: 100% !important;
  height: auto !important;
  aspect-ratio: 1000 / 1450 !important;
  
  /* 保持阴影效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4) !important;
  
  /* 平滑过渡 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 3. Vercel环境下的字体强制应用 */
.nuxt-loading,
[data-server-rendered="true"],
html[data-nuxt-ssr] {
  font-family: 'Noto Sans TC', 'Noto Sans SC', 'Noto Sans JP', 'YuGiOh-ZH', 'YuGiOh-CN', 'YuGiOh-JP', sans-serif !important;
}

/* 4. 确保在Vercel上CSS加载完成后立即应用 */
.css-loaded canvas#yugiohcard,
.css-loaded canvas.card-canvas,
.css-loaded #yugiohcard.card-canvas,
.css-loaded .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 5. 防止Vercel的CSS优化影响关键样式 */
* canvas#yugiohcard,
* canvas.card-canvas,
* #yugiohcard.card-canvas,
* .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 6. Vercel特定的布局修复 */
.vercel-deployment canvas#yugiohcard,
.vercel-deployment canvas.card-canvas,
.vercel-deployment #yugiohcard.card-canvas,
.vercel-deployment .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 7. 确保在所有可能的Vercel渲染状态下都正确显示 */
.nuxt-progress canvas#yugiohcard,
.nuxt-progress canvas.card-canvas,
.nuxt-progress #yugiohcard.card-canvas,
.nuxt-progress .card-canvas,
.layout-default canvas#yugiohcard,
.layout-default canvas.card-canvas,
.layout-default #yugiohcard.card-canvas,
.layout-default .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 8. 强制覆盖任何可能的Bootstrap或框架样式 */
.container canvas#yugiohcard,
.container canvas.card-canvas,
.container #yugiohcard.card-canvas,
.container .card-canvas,
.row canvas#yugiohcard,
.row canvas.card-canvas,
.row #yugiohcard.card-canvas,
.row .card-canvas,
.col canvas#yugiohcard,
.col canvas.card-canvas,
.col #yugiohcard.card-canvas,
.col .card-canvas,
.col-lg-6 canvas#yugiohcard,
.col-lg-6 canvas.card-canvas,
.col-lg-6 #yugiohcard.card-canvas,
.col-lg-6 .card-canvas {
  background: transparent !important;
  border: none !important;
}

/* 9. Vercel环境下的响应式修复 */
@media (max-width: 1200px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 350px !important;
    background: transparent !important;
    border: none !important;
  }
}

@media (max-width: 992px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 300px !important;
    background: transparent !important;
    border: none !important;
  }
}

@media (max-width: 768px) {
  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 280px !important;
    background: transparent !important;
    border: none !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  }
}

/* 10. 确保字体在Vercel上正确加载的回退机制 */
body {
  font-family: 'Noto Sans TC', 'Noto Sans SC', 'Noto Sans JP', 'YuGiOh-ZH', 'YuGiOh-CN', 'YuGiOh-JP', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 11. 强制在Vercel环境下立即应用样式 */
html {
  --vercel-canvas-bg: transparent !important;
  --vercel-canvas-border: none !important;
}

canvas#yugiohcard,
canvas.card-canvas,
#yugiohcard.card-canvas,
.card-canvas {
  background: var(--vercel-canvas-bg) !important;
  border: var(--vercel-canvas-border) !important;
}

/* 12. 最终保险措施 - 使用内联样式级别的优先级 */
canvas#yugiohcard[style],
canvas.card-canvas[style],
#yugiohcard.card-canvas[style],
.card-canvas[style] {
  background: transparent !important;
  border: none !important;
}

/* 13. Vercel 环境布局修复 - 确保我们的布局优化在生产环境中正确应用 */

/* 编辑面板布局修复 */
.editor-panel {
  background: var(--bg-secondary) !important;
  padding: 0 !important;
  overflow-y: auto !important;
  min-height: 85vh !important;
  max-height: 85vh !important;
  border-radius: 16px !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  display: flex !important;
  flex-direction: column !important;
}

.editor-container {
  padding: 1rem !important;
  height: 100% !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 预览面板布局修复 */
.preview-panel {
  background: var(--bg-primary) !important;
  padding: 1.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 85vh !important;
  max-height: 85vh !important;
  position: sticky !important;
  top: 80px !important;
  overflow: hidden !important;
}

.preview-container {
  width: 100% !important;
  max-width: 380px !important;
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  height: 100% !important;
}

.card-preview-wrapper {
  background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary)) !important;
  border-radius: 24px !important;
  padding: 3rem 2rem !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), inset 0 1px 0 var(--shadow-light), 0 0 0 1px var(--yugioh-gold) !important;
  border: 2px solid transparent !important;
  background-clip: padding-box !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 400px !important;
}

/* 卡片编辑器主区域 */
.card-editor-section {
  background: var(--bg-primary) !important;
  padding: 2rem 0 !important;
  margin-top: 80px !important;
  min-height: calc(100vh - 80px) !important;
}

/* 14. Vercel 环境响应式布局修复 */

/* 1200px 以下 */
@media (max-width: 1200px) {
  .preview-panel {
    min-height: 75vh !important;
    max-height: 75vh !important;
  }

  .editor-panel {
    min-height: 75vh !important;
    max-height: 75vh !important;
  }
}

/* 992px 以下 - 平板和移动端 */
@media (max-width: 992px) {
  .card-editor-section {
    padding: 1rem 0 !important;
    margin-top: 70px !important;
  }

  .card-editor-section .row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .editor-panel {
    border-right: none !important;
    border-bottom: 1px solid var(--border-color) !important;
    max-height: none !important;
    min-height: auto !important;
    margin-bottom: 1rem !important;
    position: static !important;
    order: 2 !important;
  }

  .preview-panel {
    padding: 1rem !important;
    max-height: none !important;
    min-height: auto !important;
    position: static !important;
    top: auto !important;
    order: 1 !important;
    margin-bottom: 1rem !important;
  }

  .preview-container {
    max-width: 100% !important;
    height: auto !important;
  }

  .editor-container {
    padding: 1rem !important;
  }
}

/* 768px 以下 - 手机端 */
@media (max-width: 768px) {
  .card-editor-section {
    margin-top: 60px !important;
    padding: 1rem 0 !important;
  }

  .preview-panel,
  .editor-panel {
    position: static !important;
    max-height: none !important;
    min-height: auto !important;
  }

  .preview-panel {
    order: 1 !important;
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
  }

  .editor-panel {
    order: 2 !important;
  }

  .card-preview-wrapper {
    padding: 2rem 1.5rem !important;
    min-height: 350px !important;
  }
}

/* 480px 以下 - 小屏手机 */
@media (max-width: 480px) {
  .card-editor-section {
    padding: 1rem 0 !important;
    margin-top: 70px !important;
  }

  .card-editor-section .container-fluid {
    padding: 0 0.5rem !important;
  }

  .card-editor-section .row {
    margin: 0 !important;
    gap: 1rem !important;
  }

  .card-editor-section .col-lg-5,
  .card-editor-section .col-lg-7,
  .card-editor-section .col-xl-6 {
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    flex: none !important;
  }

  .editor-container {
    padding: 0.75rem !important;
  }

  .preview-panel {
    padding: 0.75rem !important;
    order: 1 !important;
    margin-bottom: 1rem !important;
  }

  .editor-panel {
    order: 2 !important;
  }

  .card-preview-wrapper {
    padding: 1.5rem 1rem !important;
  }

  .preview-container {
    max-width: 100% !important;
    height: auto !important;
  }

  /* 防止内容溢出 */
  .container-fluid {
    overflow-x: hidden !important;
  }

  /* 表单元素优化 */
  .px-2 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
    margin-bottom: 0.75rem !important;
  }

  .row.my-3 {
    margin: 0.75rem 0 !important;
  }
}

/* 13. Vercel部署后的字体加载确认 */
.font-loaded canvas#yugiohcard,
.font-loaded canvas.card-canvas,
.font-loaded #yugiohcard.card-canvas,
.font-loaded .card-canvas {
  font-family: 'YuGiOh-Matrix', 'YuGiOh-ZH', 'YuGiOh-CN', 'YuGiOh-JP', 'Noto Sans TC', sans-serif !important;
}

/* 14. 确保在Vercel的CDN缓存更新后样式正确 */
.cache-updated canvas#yugiohcard,
.cache-updated canvas.card-canvas,
.cache-updated #yugiohcard.card-canvas,
.cache-updated .card-canvas {
  background: transparent !important;
  border: none !important;
}
