/* 关键Vercel修复样式 - 内联加载确保最高优先级 */

/* 1. 立即应用的全局重置 */
* {
  box-sizing: border-box !important;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

body {
  background: #0a0a0a !important;
  color: #ffffff !important;
  font-family: 'Noto Sans TC', 'Noto Sans SC', 'Noto Sans JP', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  line-height: 1.6 !important;
}

/* 2. 导航栏立即修复 */
.navbar {
  background: rgba(10, 10, 10, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid #333333 !important;
  z-index: 1050 !important;
  height: 80px !important;
  padding: 0.5rem 1rem !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
}

.main-navbar {
  background: rgba(10, 10, 10, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid #333333 !important;
  z-index: 1050 !important;
  height: 80px !important;
  padding: 0.5rem 1rem !important;
}

/* 3. 主要布局容器立即修复 */
.card-editor-section {
  background: #0a0a0a !important;
  padding: 2rem 0 !important;
  margin-top: 80px !important;
  min-height: calc(100vh - 80px) !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

.container-fluid {
  max-width: 100% !important;
  padding: 0 1rem !important;
  margin: 0 auto !important;
  width: 100% !important;
}

.row {
  margin: 0 !important;
  width: 100% !important;
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: stretch !important;
  min-height: 85vh !important;
}

/* 4. 预览面板立即修复 */
.preview-panel {
  background: #0a0a0a !important;
  padding: 1.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 85vh !important;
  max-height: 85vh !important;
  position: sticky !important;
  top: 80px !important;
  overflow: hidden !important;
  margin: 0 !important;
  width: 100% !important;
}

.preview-container {
  width: 100% !important;
  max-width: 380px !important;
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  height: 100% !important;
}

.card-preview-wrapper {
  background: linear-gradient(145deg, #111111, #1a1a1a) !important;
  border-radius: 24px !important;
  padding: 3rem 2rem !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 0 1px #d4af37 !important;
  border: 2px solid transparent !important;
  background-clip: padding-box !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 400px !important;
}

/* 5. 编辑面板立即修复 */
.editor-panel {
  background: #111111 !important;
  padding: 0 !important;
  overflow-y: auto !important;
  min-height: 85vh !important;
  max-height: 85vh !important;
  border-radius: 16px !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 !important;
  width: 100% !important;
}

.editor-container {
  padding: 1rem !important;
  height: 100% !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow-y: auto !important;
}

/* 6. Canvas立即修复 - 最高优先级 */
canvas#yugiohcard,
canvas.card-canvas,
#yugiohcard.card-canvas,
.card-canvas {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: none !important;
  max-width: 100% !important;
  height: auto !important;
  aspect-ratio: 1000 / 1450 !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 7. 表单元素立即修复 */
.form-control,
.form-select,
select,
textarea,
input {
  background-color: rgba(119, 119, 119, 0.29) !important;
  color: #cccccc !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 0.625rem 0.875rem !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.form-control:focus,
.form-select:focus,
select:focus,
textarea:focus,
input:focus {
  background-color: rgba(119, 119, 119, 0.4) !important;
  border-color: #4da6ff !important;
  box-shadow: 0 0 0 0.2rem rgba(77, 166, 255, 0.25) !important;
  outline: none !important;
}

/* 8. 按钮立即修复 */
.btn {
  border-radius: 8px !important;
  padding: 0.625rem 1.25rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  border: none !important;
}

.btn-primary {
  background: linear-gradient(135deg, #4da6ff, #357abd) !important;
  color: #ffffff !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #357abd, #2563eb) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(77, 166, 255, 0.3) !important;
}

/* 9. 响应式立即修复 */
@media (max-width: 992px) {
  .card-editor-section .row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .editor-panel {
    position: static !important;
    max-height: none !important;
    min-height: auto !important;
    order: 2 !important;
  }

  .preview-panel {
    position: static !important;
    max-height: none !important;
    min-height: auto !important;
    order: 1 !important;
    margin-bottom: 1rem !important;
  }

  .preview-container {
    max-width: 100% !important;
    height: auto !important;
  }
}

@media (max-width: 768px) {
  .card-editor-section {
    margin-top: 60px !important;
    padding: 1rem 0 !important;
  }

  .preview-panel,
  .editor-panel {
    position: static !important;
    max-height: none !important;
    min-height: auto !important;
  }

  .card-preview-wrapper {
    padding: 2rem 1.5rem !important;
    min-height: 350px !important;
  }

  canvas#yugiohcard,
  canvas.card-canvas,
  #yugiohcard.card-canvas,
  .card-canvas {
    max-width: 280px !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  }
}

@media (max-width: 480px) {
  .container-fluid {
    padding: 0 0.5rem !important;
  }

  .card-editor-section .col-lg-5,
  .card-editor-section .col-lg-7,
  .card-editor-section .col-xl-6 {
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    flex: none !important;
  }

  .editor-container {
    padding: 0.75rem !important;
  }

  .preview-panel {
    padding: 0.75rem !important;
  }

  .card-preview-wrapper {
    padding: 1.5rem 1rem !important;
  }
}
