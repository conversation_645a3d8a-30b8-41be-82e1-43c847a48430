/**
 * Vercel 环境布局修复插件
 * 确保在 Vercel 生产环境中布局样式正确应用
 */

export default function ({ app }) {
  // 只在客户端运行
  if (process.client) {
    // 检测是否在 Vercel 环境
    const isVercel = window.location.hostname.includes('vercel.app') ||
                    window.location.hostname.includes('.vercel.app') ||
                    process.env.VERCEL === '1' ||
                    process.env.VERCEL_ENV

    if (isVercel) {
      console.log('Vercel environment detected, applying layout fixes')
      
      // 添加 Vercel 环境标识类
      document.body.classList.add('vercel-deployment')
      
      // 等待 DOM 完全加载后应用修复
      app.router.afterEach(() => {
        setTimeout(() => {
          applyVercelLayoutFixes()
        }, 100)
      })
      
      // 页面加载完成后立即应用修复
      window.addEventListener('load', () => {
        applyVercelLayoutFixes()
      })
      
      // DOM 内容加载完成后应用修复
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyVercelLayoutFixes)
      } else {
        applyVercelLayoutFixes()
      }
    }
  }
}

/**
 * 应用 Vercel 环境布局修复
 */
function applyVercelLayoutFixes() {
  try {
    // 修复编辑面板布局
    const editorPanel = document.querySelector('.editor-panel')
    if (editorPanel) {
      editorPanel.style.setProperty('display', 'flex', 'important')
      editorPanel.style.setProperty('flex-direction', 'column', 'important')
      editorPanel.style.setProperty('min-height', '85vh', 'important')
      editorPanel.style.setProperty('max-height', '85vh', 'important')
    }

    // 修复预览面板布局
    const previewPanel = document.querySelector('.preview-panel')
    if (previewPanel) {
      previewPanel.style.setProperty('display', 'flex', 'important')
      previewPanel.style.setProperty('flex-direction', 'column', 'important')
      previewPanel.style.setProperty('justify-content', 'center', 'important')
      previewPanel.style.setProperty('min-height', '85vh', 'important')
      previewPanel.style.setProperty('max-height', '85vh', 'important')
    }

    // 修复预览容器
    const previewContainer = document.querySelector('.preview-container')
    if (previewContainer) {
      previewContainer.style.setProperty('display', 'flex', 'important')
      previewContainer.style.setProperty('flex-direction', 'column', 'important')
      previewContainer.style.setProperty('justify-content', 'center', 'important')
      previewContainer.style.setProperty('height', '100%', 'important')
    }

    // 修复编辑容器
    const editorContainer = document.querySelector('.editor-container')
    if (editorContainer) {
      editorContainer.style.setProperty('display', 'flex', 'important')
      editorContainer.style.setProperty('flex-direction', 'column', 'important')
      editorContainer.style.setProperty('flex', '1', 'important')
    }

    // 修复卡片预览包装器
    const cardPreviewWrapper = document.querySelector('.card-preview-wrapper')
    if (cardPreviewWrapper) {
      cardPreviewWrapper.style.setProperty('display', 'flex', 'important')
      cardPreviewWrapper.style.setProperty('justify-content', 'center', 'important')
      cardPreviewWrapper.style.setProperty('align-items', 'center', 'important')
      cardPreviewWrapper.style.setProperty('min-height', '400px', 'important')
    }

    // 修复 Canvas 样式
    const canvas = document.querySelector('#yugiohcard, .card-canvas')
    if (canvas) {
      canvas.style.setProperty('background', 'transparent', 'important')
      canvas.style.setProperty('border', 'none', 'important')
      canvas.style.setProperty('background-color', 'transparent', 'important')
    }

    // 响应式修复
    applyResponsiveFixes()

    console.log('Vercel layout fixes applied successfully')
  } catch (error) {
    console.warn('Error applying Vercel layout fixes:', error)
  }
}

/**
 * 应用响应式布局修复
 */
function applyResponsiveFixes() {
  const screenWidth = window.innerWidth

  // 移动端修复 (≤992px)
  if (screenWidth <= 992) {
    const row = document.querySelector('.card-editor-section .row')
    if (row) {
      row.style.setProperty('flex-direction', 'column', 'important')
      row.style.setProperty('gap', '1rem', 'important')
    }

    const editorPanel = document.querySelector('.editor-panel')
    if (editorPanel) {
      editorPanel.style.setProperty('position', 'static', 'important')
      editorPanel.style.setProperty('max-height', 'none', 'important')
      editorPanel.style.setProperty('min-height', 'auto', 'important')
      editorPanel.style.setProperty('order', '2', 'important')
    }

    const previewPanel = document.querySelector('.preview-panel')
    if (previewPanel) {
      previewPanel.style.setProperty('position', 'static', 'important')
      previewPanel.style.setProperty('max-height', 'none', 'important')
      previewPanel.style.setProperty('min-height', 'auto', 'important')
      previewPanel.style.setProperty('order', '1', 'important')
      previewPanel.style.setProperty('margin-bottom', '1rem', 'important')
    }

    const previewContainer = document.querySelector('.preview-container')
    if (previewContainer) {
      previewContainer.style.setProperty('max-width', '100%', 'important')
      previewContainer.style.setProperty('height', 'auto', 'important')
    }
  }

  // 小屏手机修复 (≤480px)
  if (screenWidth <= 480) {
    const containerFluid = document.querySelector('.card-editor-section .container-fluid')
    if (containerFluid) {
      containerFluid.style.setProperty('padding', '0 0.5rem', 'important')
      containerFluid.style.setProperty('overflow-x', 'hidden', 'important')
    }

    const cols = document.querySelectorAll('.card-editor-section .col-lg-5, .card-editor-section .col-lg-7, .card-editor-section .col-xl-6')
    cols.forEach(col => {
      col.style.setProperty('padding', '0', 'important')
      col.style.setProperty('width', '100%', 'important')
      col.style.setProperty('max-width', '100%', 'important')
      col.style.setProperty('flex', 'none', 'important')
    })
  }
}

// 监听窗口大小变化，重新应用响应式修复
if (process.client) {
  let resizeTimeout
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout)
    resizeTimeout = setTimeout(() => {
      if (document.body.classList.contains('vercel-deployment')) {
        applyResponsiveFixes()
      }
    }, 250)
  })
}
