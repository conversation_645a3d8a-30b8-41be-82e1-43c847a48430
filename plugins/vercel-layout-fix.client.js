/**
 * Vercel 环境布局修复插件 - 第四次全面修复版本
 * 确保在 Vercel 生产环境中布局样式正确应用
 */

export default function ({ app }) {
  // 只在客户端运行
  if (process.client) {
    // 检测是否在 Vercel 环境或生产环境
    const isVercel = window.location.hostname.includes('vercel.app') ||
                    window.location.hostname.includes('.vercel.app') ||
                    window.location.hostname.includes('yugiohcardmaker.org') ||
                    process.env.VERCEL === '1' ||
                    process.env.VERCEL_ENV ||
                    process.env.NODE_ENV === 'production'

    console.log('Environment check:', {
      hostname: window.location.hostname,
      isVercel,
      nodeEnv: process.env.NODE_ENV
    })

    // 在所有环境中都应用修复，确保一致性
    console.log('Applying comprehensive layout fixes')

    // 添加环境标识类
    document.body.classList.add('vercel-deployment', 'layout-fixed')
    document.documentElement.classList.add('layout-ready')

    // 立即应用关键修复
    applyImmediateFixes()

    // 等待 DOM 完全加载后应用修复
    app.router.afterEach(() => {
      setTimeout(() => {
        applyVercelLayoutFixes()
        applyCanvasFixes()
      }, 50)
    })

    // 页面加载完成后立即应用修复
    window.addEventListener('load', () => {
      applyVercelLayoutFixes()
      applyCanvasFixes()
      applyFormFixes()
    })

    // DOM 内容加载完成后应用修复
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        applyVercelLayoutFixes()
        applyCanvasFixes()
      })
    } else {
      applyVercelLayoutFixes()
      applyCanvasFixes()
    }

    // 监听字体加载完成
    if (document.fonts) {
      document.fonts.ready.then(() => {
        console.log('Fonts loaded, applying final fixes')
        applyVercelLayoutFixes()
      })
    }
  }
}

/**
 * 立即应用关键修复
 */
function applyImmediateFixes() {
  try {
    // 立即修复body样式
    document.body.style.setProperty('background', '#0a0a0a', 'important')
    document.body.style.setProperty('color', '#ffffff', 'important')
    document.body.style.setProperty('margin', '0', 'important')
    document.body.style.setProperty('padding', '0', 'important')
    document.body.style.setProperty('overflow-x', 'hidden', 'important')

    // 立即修复html样式
    document.documentElement.style.setProperty('scroll-behavior', 'smooth', 'important')
    document.documentElement.style.setProperty('font-size', '16px', 'important')

    console.log('Immediate fixes applied')
  } catch (error) {
    console.warn('Error applying immediate fixes:', error)
  }
}

/**
 * 应用 Canvas 专用修复
 */
function applyCanvasFixes() {
  try {
    const canvasSelectors = [
      '#yugiohcard',
      '.card-canvas',
      'canvas#yugiohcard',
      'canvas.card-canvas'
    ]

    canvasSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector)
      elements.forEach(canvas => {
        if (canvas) {
          canvas.style.setProperty('background', 'transparent', 'important')
          canvas.style.setProperty('background-color', 'transparent', 'important')
          canvas.style.setProperty('background-image', 'none', 'important')
          canvas.style.setProperty('border', 'none', 'important')
          canvas.style.setProperty('border-width', '0', 'important')
          canvas.style.setProperty('border-style', 'none', 'important')
          canvas.style.setProperty('border-color', 'transparent', 'important')
          canvas.style.setProperty('outline', 'none', 'important')
          canvas.style.setProperty('max-width', '100%', 'important')
          canvas.style.setProperty('height', 'auto', 'important')
          canvas.style.setProperty('aspect-ratio', '1000 / 1450', 'important')
          canvas.style.setProperty('box-shadow', '0 15px 35px rgba(0, 0, 0, 0.6), 0 5px 15px rgba(0, 0, 0, 0.4)', 'important')
        }
      })
    })

    console.log('Canvas fixes applied')
  } catch (error) {
    console.warn('Error applying canvas fixes:', error)
  }
}

/**
 * 应用表单元素修复
 */
function applyFormFixes() {
  try {
    const formSelectors = [
      '.form-control',
      '.form-select',
      'select',
      'textarea',
      'input[type="text"]',
      'input[type="number"]',
      'input[type="file"]'
    ]

    formSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector)
      elements.forEach(element => {
        if (element) {
          element.style.setProperty('background-color', 'rgba(119, 119, 119, 0.29)', 'important')
          element.style.setProperty('color', '#cccccc', 'important')
          element.style.setProperty('border', '1px solid #333333', 'important')
          element.style.setProperty('border-radius', '8px', 'important')
        }
      })
    })

    console.log('Form fixes applied')
  } catch (error) {
    console.warn('Error applying form fixes:', error)
  }
}

/**
 * 应用 Vercel 环境布局修复
 */
function applyVercelLayoutFixes() {
  try {
    // 修复主要容器
    const cardEditorSection = document.querySelector('.card-editor-section')
    if (cardEditorSection) {
      cardEditorSection.style.setProperty('background', '#0a0a0a', 'important')
      cardEditorSection.style.setProperty('padding', '2rem 0', 'important')
      cardEditorSection.style.setProperty('margin-top', '80px', 'important')
      cardEditorSection.style.setProperty('min-height', 'calc(100vh - 80px)', 'important')
      cardEditorSection.style.setProperty('width', '100%', 'important')
      cardEditorSection.style.setProperty('overflow-x', 'hidden', 'important')
    }

    // 修复容器
    const containerFluid = document.querySelector('.card-editor-section .container-fluid')
    if (containerFluid) {
      containerFluid.style.setProperty('max-width', '100%', 'important')
      containerFluid.style.setProperty('padding', '0 1rem', 'important')
      containerFluid.style.setProperty('margin', '0 auto', 'important')
      containerFluid.style.setProperty('width', '100%', 'important')
    }

    // 修复行布局
    const row = document.querySelector('.card-editor-section .row')
    if (row) {
      row.style.setProperty('margin', '0', 'important')
      row.style.setProperty('width', '100%', 'important')
      row.style.setProperty('display', 'flex', 'important')
      row.style.setProperty('flex-wrap', 'wrap', 'important')
      row.style.setProperty('align-items', 'stretch', 'important')
      row.style.setProperty('min-height', '85vh', 'important')
    }

    // 修复编辑面板布局
    const editorPanel = document.querySelector('.editor-panel')
    if (editorPanel) {
      editorPanel.style.setProperty('background', '#111111', 'important')
      editorPanel.style.setProperty('padding', '0', 'important')
      editorPanel.style.setProperty('overflow-y', 'auto', 'important')
      editorPanel.style.setProperty('min-height', '85vh', 'important')
      editorPanel.style.setProperty('max-height', '85vh', 'important')
      editorPanel.style.setProperty('border-radius', '16px', 'important')
      editorPanel.style.setProperty('border', '1px solid #333333', 'important')
      editorPanel.style.setProperty('box-shadow', '0 8px 32px rgba(0, 0, 0, 0.3)', 'important')
      editorPanel.style.setProperty('display', 'flex', 'important')
      editorPanel.style.setProperty('flex-direction', 'column', 'important')
      editorPanel.style.setProperty('margin', '0', 'important')
      editorPanel.style.setProperty('width', '100%', 'important')
    }

    // 修复预览面板布局
    const previewPanel = document.querySelector('.preview-panel')
    if (previewPanel) {
      previewPanel.style.setProperty('background', '#0a0a0a', 'important')
      previewPanel.style.setProperty('padding', '1.5rem', 'important')
      previewPanel.style.setProperty('display', 'flex', 'important')
      previewPanel.style.setProperty('flex-direction', 'column', 'important')
      previewPanel.style.setProperty('justify-content', 'center', 'important')
      previewPanel.style.setProperty('align-items', 'center', 'important')
      previewPanel.style.setProperty('min-height', '85vh', 'important')
      previewPanel.style.setProperty('max-height', '85vh', 'important')
      previewPanel.style.setProperty('position', 'sticky', 'important')
      previewPanel.style.setProperty('top', '80px', 'important')
      previewPanel.style.setProperty('overflow', 'hidden', 'important')
      previewPanel.style.setProperty('margin', '0', 'important')
      previewPanel.style.setProperty('width', '100%', 'important')
    }

    // 修复预览容器
    const previewContainer = document.querySelector('.preview-container')
    if (previewContainer) {
      previewContainer.style.setProperty('width', '100%', 'important')
      previewContainer.style.setProperty('max-width', '380px', 'important')
      previewContainer.style.setProperty('text-align', 'center', 'important')
      previewContainer.style.setProperty('display', 'flex', 'important')
      previewContainer.style.setProperty('flex-direction', 'column', 'important')
      previewContainer.style.setProperty('justify-content', 'center', 'important')
      previewContainer.style.setProperty('height', '100%', 'important')
    }

    // 修复编辑容器
    const editorContainer = document.querySelector('.editor-container')
    if (editorContainer) {
      editorContainer.style.setProperty('padding', '1rem', 'important')
      editorContainer.style.setProperty('height', '100%', 'important')
      editorContainer.style.setProperty('flex', '1', 'important')
      editorContainer.style.setProperty('display', 'flex', 'important')
      editorContainer.style.setProperty('flex-direction', 'column', 'important')
      editorContainer.style.setProperty('overflow-y', 'auto', 'important')
    }

    // 修复卡片预览包装器
    const cardPreviewWrapper = document.querySelector('.card-preview-wrapper')
    if (cardPreviewWrapper) {
      cardPreviewWrapper.style.setProperty('background', 'linear-gradient(145deg, #111111, #1a1a1a)', 'important')
      cardPreviewWrapper.style.setProperty('border-radius', '24px', 'important')
      cardPreviewWrapper.style.setProperty('padding', '3rem 2rem', 'important')
      cardPreviewWrapper.style.setProperty('box-shadow', '0 20px 60px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 0 1px #d4af37', 'important')
      cardPreviewWrapper.style.setProperty('border', '2px solid transparent', 'important')
      cardPreviewWrapper.style.setProperty('background-clip', 'padding-box', 'important')
      cardPreviewWrapper.style.setProperty('transition', 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)', 'important')
      cardPreviewWrapper.style.setProperty('position', 'relative', 'important')
      cardPreviewWrapper.style.setProperty('overflow', 'hidden', 'important')
      cardPreviewWrapper.style.setProperty('display', 'flex', 'important')
      cardPreviewWrapper.style.setProperty('justify-content', 'center', 'important')
      cardPreviewWrapper.style.setProperty('align-items', 'center', 'important')
      cardPreviewWrapper.style.setProperty('min-height', '400px', 'important')
    }

    // 响应式修复
    applyResponsiveFixes()

    console.log('Vercel layout fixes applied successfully')
  } catch (error) {
    console.warn('Error applying Vercel layout fixes:', error)
  }
}

/**
 * 应用响应式布局修复
 */
function applyResponsiveFixes() {
  const screenWidth = window.innerWidth

  // 移动端修复 (≤992px)
  if (screenWidth <= 992) {
    const row = document.querySelector('.card-editor-section .row')
    if (row) {
      row.style.setProperty('flex-direction', 'column', 'important')
      row.style.setProperty('gap', '1rem', 'important')
    }

    const editorPanel = document.querySelector('.editor-panel')
    if (editorPanel) {
      editorPanel.style.setProperty('position', 'static', 'important')
      editorPanel.style.setProperty('max-height', 'none', 'important')
      editorPanel.style.setProperty('min-height', 'auto', 'important')
      editorPanel.style.setProperty('order', '2', 'important')
    }

    const previewPanel = document.querySelector('.preview-panel')
    if (previewPanel) {
      previewPanel.style.setProperty('position', 'static', 'important')
      previewPanel.style.setProperty('max-height', 'none', 'important')
      previewPanel.style.setProperty('min-height', 'auto', 'important')
      previewPanel.style.setProperty('order', '1', 'important')
      previewPanel.style.setProperty('margin-bottom', '1rem', 'important')
    }

    const previewContainer = document.querySelector('.preview-container')
    if (previewContainer) {
      previewContainer.style.setProperty('max-width', '100%', 'important')
      previewContainer.style.setProperty('height', 'auto', 'important')
    }
  }

  // 小屏手机修复 (≤480px)
  if (screenWidth <= 480) {
    const containerFluid = document.querySelector('.card-editor-section .container-fluid')
    if (containerFluid) {
      containerFluid.style.setProperty('padding', '0 0.5rem', 'important')
      containerFluid.style.setProperty('overflow-x', 'hidden', 'important')
    }

    const cols = document.querySelectorAll('.card-editor-section .col-lg-5, .card-editor-section .col-lg-7, .card-editor-section .col-xl-6')
    cols.forEach(col => {
      col.style.setProperty('padding', '0', 'important')
      col.style.setProperty('width', '100%', 'important')
      col.style.setProperty('max-width', '100%', 'important')
      col.style.setProperty('flex', 'none', 'important')
    })
  }
}

// 监听窗口大小变化，重新应用响应式修复
if (process.client) {
  let resizeTimeout
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout)
    resizeTimeout = setTimeout(() => {
      if (document.body.classList.contains('vercel-deployment')) {
        applyResponsiveFixes()
      }
    }, 250)
  })
}
