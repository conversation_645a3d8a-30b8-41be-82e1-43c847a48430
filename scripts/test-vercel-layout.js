#!/usr/bin/env node

/**
 * Vercel 布局测试脚本
 * 用于验证 Vercel 环境中的布局修复是否正常工作
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  // 测试 URL（可以是本地或 Vercel 部署的 URL）
  url: process.env.TEST_URL || 'http://localhost:3000',
  
  // 测试的屏幕尺寸
  viewports: [
    { name: 'Desktop', width: 1920, height: 1080 },
    { name: 'Tablet', width: 768, height: 1024 },
    { name: 'Mobile', width: 375, height: 667 },
    { name: 'Small Mobile', width: 320, height: 568 }
  ],
  
  // 输出目录
  outputDir: path.join(__dirname, '../test-results'),
  
  // 测试超时时间
  timeout: 30000
};

/**
 * 主测试函数
 */
async function runLayoutTests() {
  console.log('🚀 Starting Vercel layout tests...');
  console.log(`Testing URL: ${TEST_CONFIG.url}`);
  
  // 创建输出目录
  if (!fs.existsSync(TEST_CONFIG.outputDir)) {
    fs.mkdirSync(TEST_CONFIG.outputDir, { recursive: true });
  }
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const results = [];
    
    for (const viewport of TEST_CONFIG.viewports) {
      console.log(`\n📱 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      const result = await testViewport(browser, viewport);
      results.push(result);
      
      // 输出测试结果
      if (result.success) {
        console.log(`✅ ${viewport.name}: PASSED`);
      } else {
        console.log(`❌ ${viewport.name}: FAILED`);
        result.errors.forEach(error => console.log(`   - ${error}`));
      }
    }
    
    // 生成测试报告
    await generateTestReport(results);
    
    // 检查总体结果
    const failedTests = results.filter(r => !r.success);
    if (failedTests.length === 0) {
      console.log('\n🎉 All layout tests passed!');
      process.exit(0);
    } else {
      console.log(`\n💥 ${failedTests.length} test(s) failed!`);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

/**
 * 测试特定视口尺寸
 */
async function testViewport(browser, viewport) {
  const page = await browser.newPage();
  
  try {
    // 设置视口大小
    await page.setViewport({
      width: viewport.width,
      height: viewport.height
    });
    
    // 模拟 Vercel 环境
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(window, 'location', {
        value: {
          ...window.location,
          hostname: 'test.vercel.app'
        },
        writable: true
      });
    });
    
    // 导航到页面
    await page.goto(TEST_CONFIG.url, {
      waitUntil: 'networkidle0',
      timeout: TEST_CONFIG.timeout
    });
    
    // 等待页面完全加载
    await page.waitForTimeout(2000);
    
    // 执行布局测试
    const testResult = await page.evaluate(() => {
      const errors = [];
      
      // 检查编辑面板
      const editorPanel = document.querySelector('.editor-panel');
      if (editorPanel) {
        const style = window.getComputedStyle(editorPanel);
        if (style.display !== 'flex') {
          errors.push('Editor panel display is not flex');
        }
        if (style.flexDirection !== 'column') {
          errors.push('Editor panel flex-direction is not column');
        }
      } else {
        errors.push('Editor panel not found');
      }
      
      // 检查预览面板
      const previewPanel = document.querySelector('.preview-panel');
      if (previewPanel) {
        const style = window.getComputedStyle(previewPanel);
        if (style.display !== 'flex') {
          errors.push('Preview panel display is not flex');
        }
        if (style.flexDirection !== 'column') {
          errors.push('Preview panel flex-direction is not column');
        }
      } else {
        errors.push('Preview panel not found');
      }
      
      // 检查 Canvas 样式
      const canvas = document.querySelector('#yugiohcard, .card-canvas');
      if (canvas) {
        const style = window.getComputedStyle(canvas);
        if (style.backgroundColor !== 'rgba(0, 0, 0, 0)' && style.backgroundColor !== 'transparent') {
          errors.push('Canvas background is not transparent');
        }
        if (style.border !== 'none' && style.borderWidth !== '0px') {
          errors.push('Canvas has border');
        }
      } else {
        errors.push('Canvas not found');
      }
      
      // 检查响应式布局
      const screenWidth = window.innerWidth;
      if (screenWidth <= 992) {
        const row = document.querySelector('.card-editor-section .row');
        if (row) {
          const style = window.getComputedStyle(row);
          if (style.flexDirection !== 'column') {
            errors.push('Row flex-direction is not column on mobile');
          }
        }
      }
      
      // 检查 Vercel 环境标识
      if (!document.body.classList.contains('vercel-deployment')) {
        errors.push('Vercel deployment class not found');
      }
      
      return {
        errors,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      };
    });
    
    // 截图
    const screenshotPath = path.join(TEST_CONFIG.outputDir, `${viewport.name.toLowerCase()}-layout.png`);
    await page.screenshot({
      path: screenshotPath,
      fullPage: true
    });
    
    return {
      viewport: viewport.name,
      success: testResult.errors.length === 0,
      errors: testResult.errors,
      screenshot: screenshotPath,
      actualViewport: testResult.viewport
    };
    
  } catch (error) {
    return {
      viewport: viewport.name,
      success: false,
      errors: [`Test execution error: ${error.message}`],
      screenshot: null,
      actualViewport: null
    };
  } finally {
    await page.close();
  }
}

/**
 * 生成测试报告
 */
async function generateTestReport(results) {
  const reportPath = path.join(TEST_CONFIG.outputDir, 'layout-test-report.json');
  
  const report = {
    timestamp: new Date().toISOString(),
    testUrl: TEST_CONFIG.url,
    results: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    }
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📊 Test report saved to: ${reportPath}`);
}

// 运行测试
if (require.main === module) {
  runLayoutTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runLayoutTests };
