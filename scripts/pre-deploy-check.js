#!/usr/bin/env node

/**
 * 部署前检查脚本
 * 验证所有 Vercel 布局修复文件是否正确配置
 */

const fs = require('fs');
const path = require('path');

// 检查项目配置
const CHECKS = [
  {
    name: 'Vercel 修复 CSS 文件',
    check: () => fs.existsSync(path.join(__dirname, '../assets/css/vercel-fixes.css')),
    fix: 'assets/css/vercel-fixes.css 文件缺失'
  },
  {
    name: 'Vercel 布局修复插件',
    check: () => fs.existsSync(path.join(__dirname, '../plugins/vercel-layout-fix.client.js')),
    fix: 'plugins/vercel-layout-fix.client.js 文件缺失'
  },
  {
    name: 'Vercel 关键样式脚本',
    check: () => fs.existsSync(path.join(__dirname, '../static/vercel-critical-styles.js')),
    fix: 'static/vercel-critical-styles.js 文件缺失'
  },
  {
    name: 'Nuxt 配置中的 CSS 加载顺序',
    check: () => {
      const configPath = path.join(__dirname, '../nuxt.config.js');
      if (!fs.existsSync(configPath)) return false;
      
      const config = fs.readFileSync(configPath, 'utf8');
      return config.includes('vercel-fixes.css') && 
             config.includes('vercel-layout-fix.client.js');
    },
    fix: 'nuxt.config.js 中缺少 Vercel 修复文件的引用'
  },
  {
    name: 'Vercel 配置文件',
    check: () => fs.existsSync(path.join(__dirname, '../vercel.json')),
    fix: 'vercel.json 配置文件缺失'
  },
  {
    name: 'Package.json 中的 Vercel 构建脚本',
    check: () => {
      const packagePath = path.join(__dirname, '../package.json');
      if (!fs.existsSync(packagePath)) return false;
      
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      return packageJson.scripts && packageJson.scripts['vercel-build'];
    },
    fix: 'package.json 中缺少 vercel-build 脚本'
  },
  {
    name: 'CSS 优化配置',
    check: () => {
      const configPath = path.join(__dirname, '../nuxt.config.js');
      if (!fs.existsSync(configPath)) return false;
      
      const config = fs.readFileSync(configPath, 'utf8');
      return config.includes('optimizeCSS') && 
             config.includes('discardOverridden: false') &&
             config.includes('mergeRules: false');
    },
    fix: 'nuxt.config.js 中的 CSS 优化配置不正确'
  }
];

/**
 * 运行部署前检查
 */
async function runPreDeployCheck() {
  console.log('🔍 Running pre-deployment checks for Vercel layout fixes...\n');
  
  let allPassed = true;
  const results = [];
  
  for (const check of CHECKS) {
    const passed = check.check();
    results.push({
      name: check.name,
      passed,
      fix: check.fix
    });
    
    if (passed) {
      console.log(`✅ ${check.name}`);
    } else {
      console.log(`❌ ${check.name}`);
      console.log(`   Fix: ${check.fix}`);
      allPassed = false;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 All pre-deployment checks passed!');
    console.log('✅ Ready for Vercel deployment');
    
    // 显示部署建议
    console.log('\n📋 Deployment checklist:');
    console.log('1. Run: npm run build (test local build)');
    console.log('2. Run: npm run vercel-build (test Vercel build)');
    console.log('3. Deploy to Vercel');
    console.log('4. Test layout on deployed URL');
    console.log('5. Check browser console for errors');
    
  } else {
    console.log('💥 Some checks failed!');
    console.log('❌ Please fix the issues before deploying');
    
    // 显示修复建议
    console.log('\n🔧 Quick fixes:');
    results.filter(r => !r.passed).forEach(result => {
      console.log(`- ${result.fix}`);
    });
  }
  
  // 生成检查报告
  const reportPath = path.join(__dirname, '../pre-deploy-check-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    allPassed,
    results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length
    }
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📊 Check report saved to: ${reportPath}`);
  
  return allPassed;
}

/**
 * 验证关键文件内容
 */
function validateFileContents() {
  console.log('\n🔍 Validating file contents...');
  
  // 检查 Vercel 修复 CSS 是否包含关键样式
  const vercelCssPath = path.join(__dirname, '../assets/css/vercel-fixes.css');
  if (fs.existsSync(vercelCssPath)) {
    const content = fs.readFileSync(vercelCssPath, 'utf8');
    const requiredStyles = [
      '.editor-panel',
      '.preview-panel',
      'min-height: 85vh',
      'flex-direction: column',
      '@media (max-width: 992px)'
    ];
    
    const missingStyles = requiredStyles.filter(style => !content.includes(style));
    if (missingStyles.length === 0) {
      console.log('✅ Vercel CSS contains all required styles');
    } else {
      console.log('❌ Vercel CSS missing styles:', missingStyles);
    }
  }
  
  // 检查插件是否包含关键功能
  const pluginPath = path.join(__dirname, '../plugins/vercel-layout-fix.client.js');
  if (fs.existsSync(pluginPath)) {
    const content = fs.readFileSync(pluginPath, 'utf8');
    const requiredFunctions = [
      'applyVercelLayoutFixes',
      'applyResponsiveFixes',
      'vercel-deployment',
      'setProperty'
    ];
    
    const missingFunctions = requiredFunctions.filter(func => !content.includes(func));
    if (missingFunctions.length === 0) {
      console.log('✅ Vercel plugin contains all required functions');
    } else {
      console.log('❌ Vercel plugin missing functions:', missingFunctions);
    }
  }
}

// 运行检查
if (require.main === module) {
  runPreDeployCheck()
    .then(passed => {
      if (passed) {
        validateFileContents();
      }
      process.exit(passed ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Pre-deployment check failed:', error);
      process.exit(1);
    });
}

module.exports = { runPreDeployCheck };
