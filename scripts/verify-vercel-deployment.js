#!/usr/bin/env node

/**
 * Vercel 部署验证脚本
 * 用于验证部署到 Vercel 后的布局是否正常工作
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 验证配置
const VERIFICATION_CONFIG = {
  // 要验证的 URL（从命令行参数或环境变量获取）
  url: process.argv[2] || process.env.VERCEL_URL || process.env.DEPLOYMENT_URL,
  
  // 验证项目
  checks: [
    {
      name: 'Vercel 关键样式脚本',
      path: '/vercel-critical-styles.js',
      expectedContent: 'vercel-critical-styles',
      contentType: 'application/javascript'
    },
    {
      name: '主页面可访问性',
      path: '/',
      expectedContent: 'Yu-Gi-Oh! Card Maker',
      contentType: 'text/html'
    },
    {
      name: 'CSS 文件加载',
      path: '/_nuxt/',
      expectedContent: null, // 只检查状态码
      contentType: 'text/css'
    }
  ],
  
  // 超时时间
  timeout: 10000
};

/**
 * 主验证函数
 */
async function verifyDeployment() {
  console.log('🔍 Verifying Vercel deployment...');
  
  if (!VERIFICATION_CONFIG.url) {
    console.error('❌ No URL provided. Usage: node verify-vercel-deployment.js <URL>');
    console.error('   Or set VERCEL_URL or DEPLOYMENT_URL environment variable');
    process.exit(1);
  }
  
  // 确保 URL 格式正确
  let baseUrl = VERIFICATION_CONFIG.url;
  if (!baseUrl.startsWith('http')) {
    baseUrl = `https://${baseUrl}`;
  }
  
  console.log(`📍 Testing URL: ${baseUrl}`);
  
  const results = [];
  
  // 执行基本检查
  for (const check of VERIFICATION_CONFIG.checks) {
    console.log(`\n🧪 Testing: ${check.name}`);
    
    try {
      const result = await performCheck(baseUrl, check);
      results.push(result);
      
      if (result.success) {
        console.log(`✅ ${check.name}: PASSED`);
        if (result.details) {
          console.log(`   ${result.details}`);
        }
      } else {
        console.log(`❌ ${check.name}: FAILED`);
        console.log(`   ${result.error}`);
      }
    } catch (error) {
      console.log(`❌ ${check.name}: ERROR`);
      console.log(`   ${error.message}`);
      results.push({
        name: check.name,
        success: false,
        error: error.message
      });
    }
  }
  
  // 执行布局特定检查
  console.log(`\n🎨 Testing layout-specific features...`);
  const layoutResult = await testLayoutFeatures(baseUrl);
  results.push(layoutResult);
  
  if (layoutResult.success) {
    console.log(`✅ Layout features: PASSED`);
  } else {
    console.log(`❌ Layout features: FAILED`);
    console.log(`   ${layoutResult.error}`);
  }
  
  // 生成验证报告
  const report = {
    timestamp: new Date().toISOString(),
    url: baseUrl,
    results: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    }
  };
  
  console.log('\n' + '='.repeat(50));
  
  if (report.summary.failed === 0) {
    console.log('🎉 All verification checks passed!');
    console.log('✅ Vercel deployment is working correctly');
  } else {
    console.log(`💥 ${report.summary.failed} check(s) failed!`);
    console.log('❌ Deployment may have issues');
  }
  
  console.log(`\n📊 Summary: ${report.summary.passed}/${report.summary.total} checks passed`);
  
  // 保存报告
  const fs = require('fs');
  const path = require('path');
  const reportPath = path.join(__dirname, '../vercel-deployment-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`📄 Report saved to: ${reportPath}`);
  
  return report.summary.failed === 0;
}

/**
 * 执行单个检查
 */
function performCheck(baseUrl, check) {
  return new Promise((resolve, reject) => {
    const url = new URL(check.path, baseUrl);
    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.get(url, {
      timeout: VERIFICATION_CONFIG.timeout,
      headers: {
        'User-Agent': 'Vercel-Deployment-Verifier/1.0'
      }
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const result = {
          name: check.name,
          success: false,
          statusCode: res.statusCode,
          contentType: res.headers['content-type']
        };
        
        // 检查状态码
        if (res.statusCode !== 200) {
          result.error = `HTTP ${res.statusCode}`;
          resolve(result);
          return;
        }
        
        // 检查内容类型（如果指定）
        if (check.contentType && !res.headers['content-type']?.includes(check.contentType)) {
          result.error = `Expected content-type: ${check.contentType}, got: ${res.headers['content-type']}`;
          resolve(result);
          return;
        }
        
        // 检查内容（如果指定）
        if (check.expectedContent && !data.includes(check.expectedContent)) {
          result.error = `Expected content "${check.expectedContent}" not found`;
          resolve(result);
          return;
        }
        
        result.success = true;
        result.details = `Status: ${res.statusCode}, Size: ${data.length} bytes`;
        resolve(result);
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * 测试布局特定功能
 */
async function testLayoutFeatures(baseUrl) {
  try {
    // 检查主页面是否包含我们的布局修复标识
    const mainPageResult = await performCheck(baseUrl, {
      name: 'Layout fixes',
      path: '/',
      expectedContent: 'vercel-critical-styles.js',
      contentType: 'text/html'
    });
    
    if (!mainPageResult.success) {
      return {
        name: 'Layout features',
        success: false,
        error: 'Critical styles script not found in main page'
      };
    }
    
    // 检查 Vercel 关键样式脚本
    const stylesResult = await performCheck(baseUrl, {
      name: 'Critical styles',
      path: '/vercel-critical-styles.js',
      expectedContent: 'vercel-deployment',
      contentType: 'application/javascript'
    });
    
    if (!stylesResult.success) {
      return {
        name: 'Layout features',
        success: false,
        error: 'Critical styles script not accessible or missing content'
      };
    }
    
    return {
      name: 'Layout features',
      success: true,
      details: 'All layout-specific features are working'
    };
    
  } catch (error) {
    return {
      name: 'Layout features',
      success: false,
      error: error.message
    };
  }
}

// 运行验证
if (require.main === module) {
  verifyDeployment()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyDeployment };
