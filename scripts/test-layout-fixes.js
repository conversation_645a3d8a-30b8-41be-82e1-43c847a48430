/**
 * 布局修复测试脚本
 * 验证所有关键样式是否正确应用
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing layout fixes...\n');

// 1. 检查关键CSS文件是否存在
const criticalFiles = [
  'assets/css/critical-vercel-fixes.css',
  'assets/css/vercel-fixes.css',
  'assets/css/production-fixes.css',
  'static/vercel-critical-styles.js',
  'plugins/vercel-layout-fix.client.js'
];

console.log('📁 Checking critical files:');
criticalFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, '..', file));
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
});

// 2. 检查nuxt.config.js中的CSS加载顺序
console.log('\n📋 Checking nuxt.config.js CSS order:');
const nuxtConfig = fs.readFileSync(path.join(__dirname, '..', 'nuxt.config.js'), 'utf8');

const cssOrder = [
  'critical-vercel-fixes.css',
  'bootstrap.css',
  'bootstrap-vue.css',
  'layout-stability.css',
  'performance-optimizations.css',
  'production-fixes.css',
  'vercel-fixes.css'
];

cssOrder.forEach((css, index) => {
  const found = nuxtConfig.includes(css);
  console.log(`  ${found ? '✅' : '❌'} ${index + 1}. ${css}`);
});

// 3. 检查关键脚本是否在head中
console.log('\n🔧 Checking critical script injection:');
const hasVercelScript = nuxtConfig.includes('vercel-critical-styles.js');
console.log(`  ${hasVercelScript ? '✅' : '❌'} vercel-critical-styles.js in head`);

// 4. 检查dist目录中的生成文件
console.log('\n📦 Checking generated files:');
const distPath = path.join(__dirname, '..', 'dist');
if (fs.existsSync(distPath)) {
  const hasIndex = fs.existsSync(path.join(distPath, 'index.html'));
  const hasVercelScript = fs.existsSync(path.join(distPath, 'vercel-critical-styles.js'));
  const hasCSSFiles = fs.readdirSync(path.join(distPath, '_nuxt')).some(file => file.endsWith('.css'));
  
  console.log(`  ${hasIndex ? '✅' : '❌'} index.html`);
  console.log(`  ${hasVercelScript ? '✅' : '❌'} vercel-critical-styles.js`);
  console.log(`  ${hasCSSFiles ? '✅' : '❌'} CSS files in _nuxt`);
} else {
  console.log('  ❌ dist directory not found - run npm run generate first');
}

// 5. 检查关键CSS内容
console.log('\n🎨 Checking critical CSS content:');
const criticalCSSPath = path.join(__dirname, '..', 'assets/css/critical-vercel-fixes.css');
if (fs.existsSync(criticalCSSPath)) {
  const criticalCSS = fs.readFileSync(criticalCSSPath, 'utf8');
  
  const criticalRules = [
    'box-sizing: border-box',
    'background: #0a0a0a',
    '.card-editor-section',
    '.preview-panel',
    '.editor-panel',
    'canvas#yugiohcard',
    'background: transparent',
    '@media (max-width: 992px)',
    '@media (max-width: 768px)'
  ];
  
  criticalRules.forEach(rule => {
    const found = criticalCSS.includes(rule);
    console.log(`  ${found ? '✅' : '❌'} ${rule}`);
  });
} else {
  console.log('  ❌ critical-vercel-fixes.css not found');
}

// 6. 检查Vercel修复脚本内容
console.log('\n⚡ Checking Vercel script content:');
const vercelScriptPath = path.join(__dirname, '..', 'static/vercel-critical-styles.js');
if (fs.existsSync(vercelScriptPath)) {
  const vercelScript = fs.readFileSync(vercelScriptPath, 'utf8');
  
  const scriptFeatures = [
    'Fourth comprehensive fix',
    'criticalCSS',
    'createElement',
    'insertBefore',
    'vercel-critical-loaded',
    'MutationObserver'
  ];
  
  scriptFeatures.forEach(feature => {
    const found = vercelScript.includes(feature);
    console.log(`  ${found ? '✅' : '❌'} ${feature}`);
  });
} else {
  console.log('  ❌ vercel-critical-styles.js not found');
}

// 7. 生成修复报告
console.log('\n📊 Layout Fix Summary:');
console.log('='.repeat(50));

const allChecks = [
  criticalFiles.every(file => fs.existsSync(path.join(__dirname, '..', file))),
  cssOrder.every(css => nuxtConfig.includes(css)),
  nuxtConfig.includes('vercel-critical-styles.js'),
  fs.existsSync(criticalCSSPath),
  fs.existsSync(vercelScriptPath)
];

const passedChecks = allChecks.filter(Boolean).length;
const totalChecks = allChecks.length;

console.log(`✅ Passed: ${passedChecks}/${totalChecks} checks`);

if (passedChecks === totalChecks) {
  console.log('🎉 All layout fixes are properly configured!');
  console.log('🚀 Ready for deployment to Vercel');
} else {
  console.log('⚠️  Some issues found. Please review the failed checks above.');
}

console.log('\n📝 Next steps:');
console.log('1. Run: npm run generate');
console.log('2. Deploy to Vercel');
console.log('3. Test the live site for layout issues');
console.log('4. Check browser developer tools for any CSS errors');

console.log('\n🔗 Test URLs after deployment:');
console.log('- Desktop: Check main layout and canvas display');
console.log('- Mobile: Check responsive behavior');
console.log('- Developer Tools: Check for CSS loading errors');
