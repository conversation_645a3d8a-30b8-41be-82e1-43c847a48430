/**
 * 部署前全面检查脚本
 * 确保所有修复都已正确应用
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Pre-deployment comprehensive check\n');

let allPassed = true;

// 检查函数
function check(description, condition) {
  const passed = condition();
  console.log(`${passed ? '✅' : '❌'} ${description}`);
  if (!passed) allPassed = false;
  return passed;
}

// 1. 文件存在性检查
console.log('📁 File Existence Checks:');
check('Critical CSS file exists', () => 
  fs.existsSync(path.join(__dirname, '..', 'assets/css/critical-vercel-fixes.css'))
);
check('Vercel fixes CSS exists', () => 
  fs.existsSync(path.join(__dirname, '..', 'assets/css/vercel-fixes.css'))
);
check('Production fixes CSS exists', () => 
  fs.existsSync(path.join(__dirname, '..', 'assets/css/production-fixes.css'))
);
check('Vercel critical script exists', () => 
  fs.existsSync(path.join(__dirname, '..', 'static/vercel-critical-styles.js'))
);
check('Vercel layout plugin exists', () => 
  fs.existsSync(path.join(__dirname, '..', 'plugins/vercel-layout-fix.client.js'))
);

// 2. Nuxt配置检查
console.log('\n⚙️  Nuxt Configuration Checks:');
const nuxtConfig = fs.readFileSync(path.join(__dirname, '..', 'nuxt.config.js'), 'utf8');

check('Critical CSS loaded first', () => 
  nuxtConfig.includes("'~/assets/css/critical-vercel-fixes.css'") && 
  nuxtConfig.indexOf('critical-vercel-fixes.css') < nuxtConfig.indexOf('bootstrap.css')
);
check('Vercel script in head', () => 
  nuxtConfig.includes('vercel-critical-styles.js')
);
check('Vercel plugin registered', () => 
  nuxtConfig.includes('vercel-layout-fix.client.js')
);

// 3. CSS内容检查
console.log('\n🎨 CSS Content Checks:');
const criticalCSS = fs.readFileSync(
  path.join(__dirname, '..', 'assets/css/critical-vercel-fixes.css'), 
  'utf8'
);

check('Global reset styles present', () => 
  criticalCSS.includes('box-sizing: border-box') && 
  criticalCSS.includes('margin: 0 !important')
);
check('Dark theme colors defined', () => 
  criticalCSS.includes('background: #0a0a0a') && 
  criticalCSS.includes('color: #ffffff')
);
check('Layout containers styled', () => 
  criticalCSS.includes('.card-editor-section') && 
  criticalCSS.includes('.preview-panel') && 
  criticalCSS.includes('.editor-panel')
);
check('Canvas fixes present', () => 
  criticalCSS.includes('canvas#yugiohcard') && 
  criticalCSS.includes('background: transparent !important') && 
  criticalCSS.includes('border: none !important')
);
check('Responsive breakpoints defined', () => 
  criticalCSS.includes('@media (max-width: 992px)') && 
  criticalCSS.includes('@media (max-width: 768px)') && 
  criticalCSS.includes('@media (max-width: 480px)')
);

// 4. JavaScript脚本检查
console.log('\n⚡ JavaScript Script Checks:');
const vercelScript = fs.readFileSync(
  path.join(__dirname, '..', 'static/vercel-critical-styles.js'), 
  'utf8'
);

check('Environment detection logic', () => 
  vercelScript.includes('vercel.app') && 
  vercelScript.includes('yugiohcardmaker.org')
);
check('Critical CSS injection', () => 
  vercelScript.includes('createElement') && 
  vercelScript.includes('insertBefore')
);
check('DOM ready handling', () => 
  vercelScript.includes('DOMContentLoaded') && 
  vercelScript.includes('readyState')
);
check('Mutation observer present', () => 
  vercelScript.includes('MutationObserver') && 
  vercelScript.includes('childList: true')
);
check('Canvas fix automation', () => 
  vercelScript.includes('canvas#yugiohcard') && 
  vercelScript.includes('data-fixed')
);

// 5. 插件检查
console.log('\n🔌 Plugin Checks:');
const pluginCode = fs.readFileSync(
  path.join(__dirname, '..', 'plugins/vercel-layout-fix.client.js'), 
  'utf8'
);

check('Environment detection in plugin', () => 
  pluginCode.includes('vercel.app') && 
  pluginCode.includes('NODE_ENV')
);
check('Immediate fixes function', () => 
  pluginCode.includes('applyImmediateFixes')
);
check('Canvas fixes function', () => 
  pluginCode.includes('applyCanvasFixes')
);
check('Form fixes function', () => 
  pluginCode.includes('applyFormFixes')
);
check('Responsive fixes function', () => 
  pluginCode.includes('applyResponsiveFixes')
);

// 6. 构建输出检查
console.log('\n📦 Build Output Checks:');
const distPath = path.join(__dirname, '..', 'dist');

check('Dist directory exists', () => 
  fs.existsSync(distPath)
);

if (fs.existsSync(distPath)) {
  check('Index.html generated', () => 
    fs.existsSync(path.join(distPath, 'index.html'))
  );
  check('Vercel script copied', () => 
    fs.existsSync(path.join(distPath, 'vercel-critical-styles.js'))
  );
  check('CSS files generated', () => {
    const nuxtDir = path.join(distPath, '_nuxt');
    const cssDir = path.join(nuxtDir, 'css');
    return fs.existsSync(nuxtDir) &&
           fs.existsSync(cssDir) &&
           fs.readdirSync(cssDir).some(file => file.endsWith('.css'));
  });
  
  // 检查index.html内容
  const indexHTML = fs.readFileSync(path.join(distPath, 'index.html'), 'utf8');
  check('Critical script in HTML head', () => 
    indexHTML.includes('vercel-critical-styles.js')
  );
  check('Meta viewport tag present', () => 
    indexHTML.includes('viewport') && 
    indexHTML.includes('width=device-width')
  );
}

// 7. 字体文件检查
console.log('\n🔤 Font Files Checks:');
const fontsPath = path.join(__dirname, '..', 'static/fonts');
if (fs.existsSync(fontsPath)) {
  const fontFiles = fs.readdirSync(fontsPath);
  check('YuGiOh fonts present', () => 
    fontFiles.some(f => f.includes('Matrix')) && 
    fontFiles.some(f => f.includes('jp')) && 
    fontFiles.some(f => f.includes('en'))
  );
} else {
  check('Fonts directory exists', () => false);
}

// 8. 最终总结
console.log('\n' + '='.repeat(60));
console.log('📊 DEPLOYMENT READINESS SUMMARY');
console.log('='.repeat(60));

if (allPassed) {
  console.log('🎉 ALL CHECKS PASSED! Ready for deployment.');
  console.log('\n✅ Your layout fixes are comprehensive and ready.');
  console.log('✅ Critical styles will load immediately.');
  console.log('✅ Responsive design is properly configured.');
  console.log('✅ Canvas elements will display correctly.');
  console.log('✅ Vercel environment optimizations are in place.');
  
  console.log('\n🚀 DEPLOYMENT STEPS:');
  console.log('1. Commit all changes to git');
  console.log('2. Push to your repository');
  console.log('3. Vercel will auto-deploy from git');
  console.log('4. Test the live site thoroughly');
  
  console.log('\n🔍 POST-DEPLOYMENT TESTING:');
  console.log('• Check layout on desktop (1920x1080)');
  console.log('• Check layout on tablet (768x1024)');
  console.log('• Check layout on mobile (375x667)');
  console.log('• Verify canvas displays without borders');
  console.log('• Test form interactions');
  console.log('• Check browser console for errors');
  
} else {
  console.log('❌ SOME CHECKS FAILED! Please fix the issues above.');
  console.log('\n🔧 Common fixes:');
  console.log('• Run: npm run generate');
  console.log('• Check file paths and permissions');
  console.log('• Verify CSS syntax');
  console.log('• Test JavaScript functions');
}

console.log('\n📝 Remember: This is the FOURTH comprehensive fix.');
console.log('🎯 Focus: Complete layout stability in Vercel production.');
